{"attributes": {}, "options": {"allowSelfLoops": false, "multi": false, "type": "undirected"}, "nodes": [{"key": "harry_potter", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 8, "chapter_importance": 10, "presence_level": "protagonist", "emotional_state": "nostalgic, then defiant, then grief-stricken and terrified", "actions": ["Reminisces about his time at the <PERSON><PERSON><PERSON><PERSON>' house.", "A<PERSON>ues against the decoy plan, not wanting others to risk their lives for him.", "Reluctantly provides a hank of his hair for the Polyjuice Potion.", "Fights pursuing Death Eaters with spells like 'Stupefy', 'Impedimenta', and 'Expelliarmus'.", "Witnesses <PERSON><PERSON><PERSON>'s death and shouts in denial and grief.", "His wand acts on its own, producing golden fire to defend against Vol<PERSON><PERSON><PERSON>.", "Punches the motorbike's dragon-fire button to escape.", "Crashes Sirius's motorbike into a muddy pond."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON> is confronted with the severe costs of war, losing friends and mentors. He solidifies his moral stance against killing, even when criticized by allies. His assertion of trust in the face of possible betrayal shows a maturation, though <PERSON><PERSON> sees it as a dangerous echo of his father's tragic flaw. The return of his scar-link to <PERSON><PERSON><PERSON><PERSON> re-establishes a critical vulnerability.", "community": 1, "url": "#character/harry_potter", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "alastor_moody", "attributes": {"label": "<PERSON><PERSON><PERSON> '<PERSON>-Eye' <PERSON>", "name": "<PERSON><PERSON><PERSON> '<PERSON>-Eye' <PERSON>", "stories": 4, "chapter_importance": 8, "presence_level": "major", "emotional_state": "gruff, impatient, authoritative", "actions": ["Arrives at Privet Drive with a change of plan.", "Explains the threat from <PERSON> and the necessity of using decoys.", "Authoritatively overrules <PERSON>'s objections to the plan.", "Distributes the Polyjuice Potion and organizes the pairs for travel.", "Gives the command for the group to leave simultaneously.", "Chooses to fly with <PERSON><PERSON><PERSON><PERSON> to keep an eye on him."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "His death establishes him as the 'Fallen Warrior' of the chapter title and serves as a major blow to the Order, removing one of its toughest and most experienced members and escalating the sense of dread and loss.", "community": 0, "url": "#character/alastor_moody", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "hagrid", "attributes": {"label": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "stories": 1, "chapter_importance": 8, "presence_level": "major", "emotional_state": "cheerful, then anxious and determined", "actions": ["Arrives on Sirius Black's flying motorbike.", "Pilots the motorbike with <PERSON> in the sidecar.", "Deploys several modified defenses on the bike, including a brick wall, a net, and dragon fire.", "Attempts to magically repair the sidecar, causing it to break off.", "Hoists <PERSON> from the falling sidecar onto the bike's seat.", "Launches himself off the bike to attack a Death Eater."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "Demonstrates his immense bravery and unwavering loyalty to <PERSON>, willing to sacrifice himself without hesitation.", "community": 0, "url": "#character/hagrid", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "lord_vol<PERSON><PERSON>t", "attributes": {"label": "Lord <PERSON>", "name": "Lord <PERSON>", "stories": 3, "chapter_importance": 9, "presence_level": "major", "emotional_state": "furious and determined", "actions": ["Appears in the sky, flying without a broomstick or thestral.", "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> directly.", "Screams 'Mine!' when preparing to attack <PERSON>.", "Screams 'No!' in fury when <PERSON>'s wand repels his curse.", "Demands another Death Eater's wand ('<PERSON><PERSON><PERSON>, give me your wand!') to continue the attack.", "Vanishes just as <PERSON> crashes."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON><PERSON>'s power and ruthlessness are heavily reinforced. His ability to fly is a new, terrifying development, and his targeted killing of <PERSON><PERSON><PERSON> and torture of <PERSON><PERSON><PERSON><PERSON> demonstrate his strategic and merciless nature.", "community": 0, "url": "#character/lord_voldemort", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "hermione_granger", "attributes": {"label": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "stories": 5, "chapter_importance": 6, "presence_level": "supporting", "emotional_state": "happy to see <PERSON>, then determined", "actions": ["Arrives at Privet Drive and flings her arms around <PERSON>.", "Lines up and drinks the Polyjuice Potion to become a Harry decoy.", "Comments that <PERSON>'s potion looks tastier than <PERSON><PERSON>'s.", "Puts on glasses and complains about <PERSON>'s awful eyesight.", "Is paired with <PERSON> to ride a thestral."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON>'s character is defined by immense sacrifice and foresight in this chapter. Her decision to erase her parents' memories is a shocking display of maturity and dedication to the cause, establishing her as an equal partner in the mission's burdens, not just a follower.", "community": 4, "url": "#character/hermione_granger", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "ron_weasley", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 5, "chapter_importance": 6, "presence_level": "supporting", "emotional_state": "cheerful, then slightly uncomfortable", "actions": ["Greets <PERSON> by clapping him on the back.", "Takes the Polyjuice Potion to become a Harry decoy.", "Looks down at his bare chest and comments on <PERSON><PERSON><PERSON> lying about a tattoo.", "Is paired with <PERSON><PERSON> to ride a broom.", "Throws a 'furtive, guilty look at <PERSON><PERSON>' before putting his hands on <PERSON><PERSON>'s waist."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON>'s protective instincts as a brother are highlighted, causing friction with <PERSON>. His unexpected knowledge of wizarding children's stories also provides a rare moment where he is more knowledgeable than <PERSON><PERSON><PERSON>, showcasing a different side of his upbringing and character.", "community": 4, "url": "#character/ron_weasley", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "fred_weasley", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 2, "chapter_importance": 5, "presence_level": "supporting", "emotional_state": "jovial and earnest", "actions": ["Takes the Polyjuice Potion.", "Jokes with <PERSON> about being identical.", "Tries to lighten the mood by joking about being stuck as a 'specky, scrawny git'.", "Deliberately confuses <PERSON> about his identity before admitting he is <PERSON>.", "Is paired with his father, <PERSON>."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON>'s initial terror shows a rare crack in his jocular persona, revealing the depth of his bond with <PERSON>. His quick return to banter and his support for <PERSON> show his loyalty and coping mechanisms.", "community": 4, "url": "#character/fred_weasley", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "george_weasley", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 2, "chapter_importance": 5, "presence_level": "supporting", "emotional_state": "jovial and sarcastic", "actions": ["Takes the Polyjuice Potion.", "Jokes with <PERSON> about being identical.", "Tells <PERSON> there's no chance of getting his hair unless he cooperates, sarcastically.", "Is initially misidentified as <PERSON> by <PERSON>.", "Is paired with <PERSON><PERSON>."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON>'s character is defined in this chapter by his resilience and humor in the face of horrific injury, a core trait he shares with his twin, <PERSON>. His injury is a stark, physical symbol of the war's cost.", "community": 4, "url": "#character/george_weasley", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "mundungus_fletcher", "attributes": {"label": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "stories": 2, "chapter_importance": 4, "presence_level": "supporting", "emotional_state": "reluctant and cowardly", "actions": ["Arrives with the group looking 'small, dirty, and hangdog'.", "Is forced by Hagrid to line up for the Polyjuice Potion.", "Protests being a decoy, saying he'd rather be a protector.", "Drinks the potion and travels with <PERSON>."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON><PERSON> is revealed to be a coward whose actions have catastrophic consequences, directly leading to the death of a key ally. This complicates the 'traitor' question, posing cowardice as an equally destructive force.", "community": 0, "url": "#character/mundungus_fletcher", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "nymphadora_tonks", "attributes": {"label": "Nymph<PERSON><PERSON>", "name": "Nymph<PERSON><PERSON>", "stories": 1, "chapter_importance": 5, "presence_level": "supporting", "emotional_state": "bright and happy", "actions": ["Arrives with her hair a 'favorite shade of bright pink'.", "Happily reveals to <PERSON> that she and <PERSON><PERSON> are married.", "<PERSON> paired with <PERSON> for the flight.", "Knocks over a mug tree while waving at <PERSON>."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "Presents a moment of personal joy and normalcy amidst the growing tension.", "community": 0, "url": "#character/nymphadora_tonks", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "remus_lupin", "attributes": {"label": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "stories": 2, "chapter_importance": 4, "presence_level": "supporting", "emotional_state": "somber, reserved", "actions": ["Arrives looking 'grayer, more lined'.", "His marriage to <PERSON><PERSON> is revealed.", "Points out they are one short for the decoys.", "Is paired with <PERSON> (as <PERSON>)."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON> is shown as a hardened, pragmatic wartime leader. His usual calm demeanor is replaced by tension, suspicion, and frustration, highlighting the immense pressure and paranoia the Order is under. His comparison of <PERSON> to <PERSON> adds a layer of tragic foresight.", "community": 0, "url": "#character/remus_lupin", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "fleur_delacour", "attributes": {"label": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "stories": 1, "chapter_importance": 4, "presence_level": "supporting", "emotional_state": "disgusted (by <PERSON><PERSON><PERSON><PERSON>), vain, affectionate (towards <PERSON>)", "actions": ["Takes the Polyjuice Potion.", "Wrinkles her nose when <PERSON><PERSON><PERSON><PERSON> is placed next to her.", "Looks at her reflection and declares herself ''ideous' as <PERSON>.", "Is paired with <PERSON> to ride a thestral as she dislikes brooms."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "Despite her vanity and pickiness, she willingly makes herself 'hideous' to help <PERSON>.", "community": 0, "url": "#character/fleur_delacour", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "bill_weasley", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 2, "chapter_importance": 3, "presence_level": "supporting", "emotional_state": "calm, protective", "actions": ["Arrives with the group, 'badly scarred and long-haired'.", "States he is taking <PERSON><PERSON><PERSON> on a thestral.", "Helps F<PERSON>ur onto the thestral."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON> steps into a leadership role, delivering the hardest news with grim resolve and offering a rational counter-argument to the immediate suspicion of betrayal. His sense of duty is paramount.", "community": 4, "url": "#character/bill_weasley", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "art<PERSON>_weasley", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 1, "chapter_importance": 3, "presence_level": "supporting", "emotional_state": "kind, cautious", "actions": ["Arrives with the group.", "<PERSON><PERSON> to be careful with the modified motorbike.", "Is paired with <PERSON> (as <PERSON>)."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "Shows his characteristic caution and concern for safety.", "community": 4, "url": "#character/arthur_weasley", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "kingsley_shacklebolt", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 3, "chapter_importance": 3, "presence_level": "supporting", "emotional_state": "calm, reassuring", "actions": ["Explains he left his job of protecting the Muggle Prime Minister because <PERSON> is more important.", "Is paired with <PERSON><PERSON><PERSON> to ride a thestral.", "Helps <PERSON><PERSON><PERSON> onto the thestral."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "Acts as the messenger that ends the story's period of calm and officially begins the open war.", "community": 0, "url": "#character/kingsley_shacklebolt", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "hedwig", "attributes": {"label": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "stories": 3, "chapter_importance": 7, "presence_level": "minor", "emotional_state": "sulking, then presumably terrified", "actions": ["Sulks with her head under her wing.", "Is placed in the motorbike's sidecar.", "Is struck by a green jet of light (Killing Curse).", "Falls to the floor of her cage, dead."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON>'s death is the first major loss of the chapter, symbolizing the end of <PERSON>'s childhood and his link to a more innocent time in the magical world. Her death deeply affects <PERSON>.", "community": 0, "url": "#character/hedwig", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "stanley_shunpike", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 1, "chapter_importance": 6, "presence_level": "minor", "emotional_state": "blank", "actions": ["Appears as a pursuing Death Eater.", "His hood slips, revealing his 'strangely blank face'.", "Is targeted by <PERSON>'s 'Expel<PERSON><PERSON><PERSON>' spell."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "Revealed to be working with the Death Eaters, though his blank face suggests he is not doing so willingly.", "community": 0, "url": "#character/stanley_shunpike", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "pius_thicknesse", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 2, "chapter_importance": 5, "presence_level": "mentioned", "emotional_state": "unknown", "actions": ["Has 'gone over' to <PERSON><PERSON><PERSON><PERSON>'s side.", "Made it an imprisonable offense to connect the Dursley house to the Floo Network, place a Portkey there, or Apparate in or out.", "Effectively trapped <PERSON> at Privet Drive under the guise of protecting him."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "Introduced as a high-ranking Ministry official now allied with <PERSON><PERSON><PERSON><PERSON>, demonstrating the Ministry's fall.", "community": 0, "url": "#character/pius_thicknesse", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "dumbledore", "attributes": {"label": "Albus Dumbledore", "name": "Albus Dumbledore", "stories": 1, "chapter_importance": 3, "presence_level": "mentioned", "emotional_state": "unknown", "actions": ["Once walked through the <PERSON><PERSON><PERSON><PERSON>' front door.", "Stated that You-Know-Who would want to finish <PERSON> in person."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "His past actions and wisdom continue to influence the Order's strategies.", "community": 3, "url": "#character/dumbledore", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "dudley_dursley", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 2, "chapter_importance": 1, "presence_level": "mentioned", "emotional_state": "unknown", "actions": ["Was seen leaving in a car with his parents.", "Once puked on the doormat after <PERSON> saved him from Demento<PERSON>."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "Shows significant growth, evolving from a bully into someone capable of introspection, gratitude, and concern for <PERSON>, marking a moment of redemption.", "community": 2, "url": "#character/dudley_dursley", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "<PERSON><PERSON><PERSON>", "attributes": {"label": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "stories": 1, "chapter_importance": 2, "presence_level": "mentioned", "emotional_state": "unknown", "actions": ["Is ordered by <PERSON><PERSON><PERSON><PERSON> to give him his wand."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "Mentioned only as a Death Eater whose wand <PERSON><PERSON><PERSON><PERSON> demands, showing <PERSON><PERSON><PERSON><PERSON>'s dominance.", "community": 0, "url": "#character/selwyn", "x": 0, "y": 0, "size": 1, "color": "#666666"}}], "edges": [{"source": "harry_potter", "target": "lord_vol<PERSON><PERSON>t", "attributes": {"weight": 10, "relationship_type": "conflict", "interaction_summary": "<PERSON> and <PERSON><PERSON><PERSON><PERSON> engage in a direct aerial battle. <PERSON><PERSON><PERSON><PERSON> pursues <PERSON> with the intent to kill, but is foiled by a mysterious magical protection from <PERSON>'s wand, which acts on its own to defend <PERSON>.", "emotional_intensity": 10, "plot_significance": 10}}, {"source": "harry_potter", "target": "hagrid", "attributes": {"weight": 9, "relationship_type": "protection", "interaction_summary": "<PERSON><PERSON><PERSON> acts as <PERSON>'s pilot and protector during the escape. Despite numerous setbacks, including the sidecar breaking off, <PERSON><PERSON><PERSON>'s primary focus is <PERSON>'s safety, culminating in him tackling a Death Eater off his broom to save <PERSON>.", "emotional_intensity": 9, "plot_significance": 9}}, {"source": "harry_potter", "target": "hedwig", "attributes": {"weight": 8, "relationship_type": "family", "interaction_summary": "<PERSON> speaks to <PERSON><PERSON><PERSON> nostalgically before they leave, only to witness her being killed by a curse moments into their flight. The event is a source of immense and immediate grief for <PERSON>.", "emotional_intensity": 10, "plot_significance": 8}}, {"source": "harry_potter", "target": "alastor_moody", "attributes": {"weight": 7, "relationship_type": "conflict", "interaction_summary": "<PERSON>, as the leader of the operation, presents a plan that <PERSON> vehemently objects to. <PERSON> uses his authority to dismiss <PERSON>'s concerns and force his cooperation for his own safety.", "emotional_intensity": 6, "plot_significance": 7}}, {"source": "harry_potter", "target": "hermione_granger", "attributes": {"weight": 6, "relationship_type": "friendship", "interaction_summary": "<PERSON><PERSON><PERSON> greets <PERSON> with a warm hug and shows her steadfast loyalty by becoming a decoy. Their interaction is characteristic of their close friendship and her practical nature.", "emotional_intensity": 5, "plot_significance": 5}}, {"source": "alastor_moody", "target": "mundungus_fletcher", "attributes": {"weight": 6, "relationship_type": "conflict", "interaction_summary": "<PERSON> treats <PERSON><PERSON><PERSON><PERSON> with open contempt, forcing the reluctant and cowardly wizard to act as a decoy. <PERSON> explicitly states he is partnering with <PERSON><PERSON><PERSON><PERSON> simply to keep an eye on him.", "emotional_intensity": 5, "plot_significance": 4}}, {"source": "nymphadora_tonks", "target": "remus_lupin", "attributes": {"weight": 5, "relationship_type": "romance", "interaction_summary": "<PERSON><PERSON> excitedly reveals to <PERSON> that she and <PERSON><PERSON> have recently gotten married. The brief interaction serves as a moment of happiness and personal news before the danger begins.", "emotional_intensity": 4, "plot_significance": 3}}, {"source": "fleur_delacour", "target": "bill_weasley", "attributes": {"weight": 5, "relationship_type": "romance", "interaction_summary": "<PERSON> acts as a protective partner for <PERSON><PERSON><PERSON>, accommodating her dislike of brooms by taking her on a thestral. <PERSON><PERSON><PERSON>, in turn, gives him an affectionate look.", "emotional_intensity": 4, "plot_significance": 2}}]}